import { Game } from './core/Game.js';
import { CanvasRenderer } from './ui/CanvasRenderer.js';
import { MenuState } from './states/MenuState.js';
import { PlayState } from './states/PlayState.js';
import { EndState } from './states/EndState.js';

const canvas = document.getElementById('game-canvas');
const renderer = new CanvasRenderer(canvas);
const game = new Game(renderer);

// Kiểm tra xem có màu đã lưu trong localStorage không
const savedColor = localStorage.getItem('selectedColor');
if (savedColor) {
    // Nếu có màu đã lưu, bắt đầu game với màu đó
    const playState = new PlayState(game, savedColor);
    game.setState(playState);
} else {
    // Nếu không có màu đã lưu, hiển thị menu
    game.setState(new MenuState(game));
}

game.start();
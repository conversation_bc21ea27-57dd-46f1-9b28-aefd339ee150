import { PlayState } from './PlayState.js';

export class MenuState {
    constructor(game) {
        this.game = game;
        this.selectedColor = null;
        this.setupEventListeners();
    }

    setupEventListeners() {
        const canvas = document.getElementById('game-canvas');
        canvas.addEventListener('click', this.handleMenuClick.bind(this));
    }

    handleMenuClick(event) {
        const canvas = event.target;
        const rect = canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // Vùng chọn màu trắng
        if (x > this.game.renderer.size / 4 && 
            x < this.game.renderer.size / 2 && 
            y > this.game.renderer.size / 2 && 
            y < this.game.renderer.size * 3/4) {
            this.selectedColor = 'white';
            this.startGame();
        }
        
        // Vùng chọn màu đen
        if (x > this.game.renderer.size / 2 && 
            x < this.game.renderer.size * 3/4 && 
            y > this.game.renderer.size / 2 && 
            y < this.game.renderer.size * 3/4) {
            this.selectedColor = 'black';
            this.startGame();
        }
    }

    startGame() {
        const playState = new PlayState(this.game, this.selectedColor);
        this.game.setState(playState);
    }

    update(delta) {
        // Xử lý logic menu
    }

    render(renderer) {
        renderer.ctx.clearRect(0, 0, renderer.size, renderer.size);
        renderer.ctx.fillStyle = '#333';
        renderer.ctx.fillRect(0, 0, renderer.size, renderer.size);
        renderer.ctx.fillStyle = '#fff';
        renderer.ctx.font = '48px Arial';
        renderer.ctx.textAlign = 'center';
        renderer.ctx.fillText('Chess Game', renderer.size / 2, renderer.size / 2 - 100);
        renderer.ctx.font = '24px Arial';
        renderer.ctx.fillText('Chọn màu quân', renderer.size / 2, renderer.size / 2 - 40);
        renderer.ctx.fillStyle = '#fff';
        renderer.ctx.strokeStyle = '#000';
        renderer.ctx.fillRect(
            renderer.size / 4, 
            renderer.size / 2, 
            renderer.size / 4, 
            renderer.size / 4
        );
        renderer.ctx.strokeRect(
            renderer.size / 4, 
            renderer.size / 2, 
            renderer.size / 4, 
            renderer.size / 4
        );
        renderer.ctx.fillStyle = '#000';
        renderer.ctx.fillText('Trắng', renderer.size / 2 - 50, renderer.size / 2 + 50);
        renderer.ctx.fillStyle = '#000';
        renderer.ctx.fillRect(
            renderer.size / 2, 
            renderer.size / 2, 
            renderer.size / 4, 
            renderer.size / 4
        );
        renderer.ctx.strokeStyle = '#fff';
        renderer.ctx.strokeRect(
            renderer.size / 2, 
            renderer.size / 2, 
            renderer.size / 4, 
            renderer.size / 4
        );
        renderer.ctx.fillStyle = '#fff';
        renderer.ctx.fillText('Đen', renderer.size / 2 + 50, renderer.size / 2 + 50);
    }
} 
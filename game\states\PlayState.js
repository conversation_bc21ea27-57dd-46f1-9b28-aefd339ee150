import { Board } from '../core/Board.js';
import { Piece } from '../core/Piece.js';

export class PlayState {
    constructor(game, playerColor = 'white') {
        this.game = game;
        this.playerColor = playerColor;
        this.board = new Board();
        this.initializeBoard(this.playerColor);
        
        // Thêm sự kiện click chuột
        this.selectedPiece = null;
        this.selectedSquare = null;
        this.possibleMoves = [];
        this.setupEventListeners();
    }

    setupEventListeners() {
        const canvas = document.getElementById('game-canvas');
        canvas.addEventListener('click', this.handleBoardClick.bind(this));
    }

    handleBoardClick(event) {
        const canvas = event.target;
        const rect = canvas.getBoundingClientRect();
        const x = Math.floor((event.clientX - rect.left) / (canvas.width / 8));
        const y = Math.floor((event.clientY - rect.top) / (canvas.height / 8));

        // Điều chỉnh tọa độ để luôn phù hợp với màu người chơi
        const adjustedX = this.playerColor === 'white' ? x : 7 - x;
        const adjustedY = this.playerColor === 'white' ? y : 7 - y;

        const clickedPiece = this.board.grid[adjustedY][adjustedX];

        if (!this.selectedPiece) {
            // Chọn quân cờ
            if (clickedPiece && clickedPiece.color === this.playerColor) {
                this.selectedPiece = clickedPiece;
                this.selectedSquare = { x: adjustedX, y: adjustedY };
                
                // Tìm các nước đi hợp lệ
                this.possibleMoves = this.findValidMoves(adjustedX, adjustedY);
            }
        } else {
            // Di chuyển quân cờ
            const isValidMove = this.possibleMoves.some(move => 
                move.x === adjustedX && move.y === adjustedY
            );

            if (isValidMove) {
                // Thực hiện di chuyển
                this.board.grid[adjustedY][adjustedX] = this.selectedPiece;
                this.board.grid[this.selectedSquare.y][this.selectedSquare.x] = null;
                
                // Đánh dấu quân cờ đã di chuyển
                this.selectedPiece.hasMoved = true;
            }

            // Bỏ chọn quân cờ và xóa các nước đi
            this.selectedPiece = null;
            this.selectedSquare = null;
            this.possibleMoves = [];
        }

        // Vẽ lại bàn cờ
        this.renderBoard();
    }

    findValidMoves(x, y) {
        const piece = this.board.grid[y][x];
        if (!piece) return [];

        // Lấy các nước đi hợp lệ của quân cờ
        return piece.getPossibleMoves(this.board, x, y);
    }

    renderBoard() {
        // Vẽ bàn cờ với các nước đi hợp lệ
        this.game.renderer.drawBoard(
            this.board.grid, 
            this.selectedSquare, 
            this.possibleMoves,
            this.playerColor
        );
    }

    initializeBoard(playerColor) {
        // Khởi tạo quân cờ ban đầu
        const pieces = [
            // Quân trắng
            {type: 'rook', color: 'white', positions: ['a1', 'h1']},
            {type: 'knight', color: 'white', positions: ['b1', 'g1']},
            {type: 'bishop', color: 'white', positions: ['c1', 'f1']},
            {type: 'queen', color: 'white', positions: ['d1']},
            {type: 'king', color: 'white', positions: ['e1']},
            // Quân tốt trắng
            ...['a2', 'b2', 'c2', 'd2', 'e2', 'f2', 'g2', 'h2'].map(pos => ({type: 'pawn', color: 'white', positions: [pos]})),
            
            // Quân đen
            {type: 'rook', color: 'black', positions: ['a8', 'h8']},
            {type: 'knight', color: 'black', positions: ['b8', 'g8']},
            {type: 'bishop', color: 'black', positions: ['c8', 'f8']},
            {type: 'queen', color: 'black', positions: ['d8']},
            {type: 'king', color: 'black', positions: ['e8']},
            // Quân tốt đen
            ...['a7', 'b7', 'c7', 'd7', 'e7', 'f7', 'g7', 'h7'].map(pos => ({type: 'pawn', color: 'black', positions: [pos]})),
        ];

        // Reset grid
        this.board.grid = Array(8).fill(null).map(() => Array(8).fill(null));

        pieces.forEach(pieceData => {
            pieceData.positions.forEach(pos => {
                const [x, y] = this.positionToCoord(pos);
                
                // Nếu chọn đen, đảo ngược vị trí quân cờ
                const adjustedX = playerColor === 'black' ? 7 - x : x;
                const adjustedY = playerColor === 'black' ? 7 - y : y;
                
                // Xác định màu quân cờ dựa trên màu người chơi
                const finalColor = playerColor === 'white' ? 
                    (pieceData.color === 'white' ? 'white' : 'black') :
                    (pieceData.color === 'white' ? 'black' : 'white');
                
                const finalPiece = new Piece(finalColor, pieceData.type);
                
                this.board.grid[adjustedY][adjustedX] = finalPiece;
            });
        });
    }

    positionToCoord(pos) {
        const file = pos[0].toLowerCase().charCodeAt(0) - 97;
        const rank = 8 - parseInt(pos[1]);
        return [file, rank];
    }

    update(delta) {
        // Xử lý logic chơi game
    }

    render(renderer) {
        // Vẽ bàn cờ và quân cờ
        this.renderBoard();
    }
} 